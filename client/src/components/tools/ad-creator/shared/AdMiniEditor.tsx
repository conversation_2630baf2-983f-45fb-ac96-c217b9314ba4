/**
 * AdMiniEditor - Mini editor de imágenes para anuncios generados
 * Permite borrar partes de la imagen y enviar a Polotno para edición avanzada
 */

import React, { useState, useRef, useEffect, Suspense, lazy } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Eraser, Undo, Download, Edit3, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/hooks/use-toast';
import { GeneratedAd } from '@/types/ad-creator-types';

// Lazy load PolotnoStudio
const PolotnoStudio = lazy(() => import('@/components/polotno/PolotnoStudio').then(module => ({
  default: module.PolotnoStudio
})));

interface AdMiniEditorProps {
  ad: GeneratedAd;
  isOpen: boolean;
  onClose: () => void;
  onSave: (editedAd: GeneratedAd) => void;
}

export function AdMiniEditor({ ad, isOpen, onClose, onSave }: AdMiniEditorProps) {
  const { toast } = useToast();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [brushSize, setBrushSize] = useState(20);
  const [hasMask, setHasMask] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [originalImageData, setOriginalImageData] = useState<ImageData | null>(null);
  const [showPolotnoModal, setShowPolotnoModal] = useState(false);
  const [polotnoImageData, setPolotnoImageData] = useState<string | null>(null);

  // Cargar imagen en el canvas cuando se abre el modal
  useEffect(() => {
    if (isOpen && ad.image_url) {
      loadImageToCanvas();
    }
  }, [isOpen, ad.image_url]);

  const loadImageToCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.src = ad.image_url;

    img.onload = () => {
      // Hacer el canvas cuadrado respetando las dimensiones de la imagen
      const maxSize = 500; // Tamaño máximo del canvas
      const aspectRatio = img.width / img.height;

      if (aspectRatio > 1) {
        // Imagen más ancha que alta
        canvas.width = maxSize;
        canvas.height = maxSize / aspectRatio;
      } else {
        // Imagen más alta que ancha o cuadrada
        canvas.height = maxSize;
        canvas.width = maxSize * aspectRatio;
      }

      // Dibujar la imagen respetando sus proporciones
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      // Guardar los datos originales de la imagen
      setOriginalImageData(ctx.getImageData(0, 0, canvas.width, canvas.height));
      setImageLoaded(true);
      setHasMask(false);
    };

    img.onerror = () => {
      toast({
        title: "Error",
        description: "No se pudo cargar la imagen",
        variant: "destructive",
      });
    };
  };

  // Funciones de dibujo para la máscara
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    draw(e);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const x = ((e.clientX - rect.left) * canvas.width) / rect.width;
    const y = ((e.clientY - rect.top) * canvas.height) / rect.height;

    // Configurar el pincel para la máscara (rojo semi-transparente)
    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
    ctx.beginPath();
    ctx.arc(x, y, brushSize / 2, 0, 2 * Math.PI);
    ctx.fill();

    setHasMask(true);
  };

  const endDrawing = () => {
    setIsDrawing(false);
  };

  // Limpiar la máscara
  const clearMask = () => {
    if (!originalImageData) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Restaurar la imagen original
    ctx.putImageData(originalImageData, 0, 0);
    setHasMask(false);
  };

  // Procesar la imagen con la máscara (borrar objetos)
  const handleEraseObjects = async () => {
    if (!hasMask || !originalImageData) {
      toast({
        title: "Error",
        description: "Debes dibujar sobre las áreas que deseas borrar",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const canvas = canvasRef.current;
      if (!canvas) throw new Error("Canvas no disponible");

      // Crear canvas para la imagen original
      const originalCanvas = document.createElement('canvas');
      originalCanvas.width = canvas.width;
      originalCanvas.height = canvas.height;
      const originalCtx = originalCanvas.getContext('2d');
      if (!originalCtx) throw new Error("No se pudo crear el contexto del canvas original");

      // Restaurar la imagen original
      originalCtx.putImageData(originalImageData, 0, 0);

      // Crear canvas para la máscara
      const maskCanvas = document.createElement('canvas');
      maskCanvas.width = canvas.width;
      maskCanvas.height = canvas.height;
      const maskCtx = maskCanvas.getContext('2d');
      if (!maskCtx) throw new Error("No se pudo crear el contexto del canvas de máscara");

      // Crear máscara en blanco y negro
      maskCtx.fillStyle = 'black';
      maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);

      // Obtener los datos del canvas actual (con las marcas rojas)
      const currentImageData = canvas.getContext('2d')?.getImageData(0, 0, canvas.width, canvas.height);
      if (!currentImageData) throw new Error("No se pudieron obtener los datos de la imagen actual");

      // Convertir las áreas rojas a blanco en la máscara
      const maskImageData = maskCtx.createImageData(maskCanvas.width, maskCanvas.height);
      for (let i = 0; i < currentImageData.data.length; i += 4) {
        const r = currentImageData.data[i];
        const g = currentImageData.data[i + 1];
        const b = currentImageData.data[i + 2];

        // Si es rojo (área marcada para borrar), hacer blanco en la máscara
        if (r > 200 && g < 100 && b < 100) {
          maskImageData.data[i] = 255;     // R
          maskImageData.data[i + 1] = 255; // G
          maskImageData.data[i + 2] = 255; // B
          maskImageData.data[i + 3] = 255; // A
        } else {
          maskImageData.data[i] = 0;       // R
          maskImageData.data[i + 1] = 0;   // G
          maskImageData.data[i + 2] = 0;   // B
          maskImageData.data[i + 3] = 255; // A
        }
      }
      maskCtx.putImageData(maskImageData, 0, 0);

      // Convertir ambos canvas a blobs
      const [originalBlob, maskBlob] = await Promise.all([
        new Promise<Blob>((resolve) => {
          originalCanvas.toBlob((blob) => {
            if (blob) resolve(blob);
          }, 'image/png');
        }),
        new Promise<Blob>((resolve) => {
          maskCanvas.toBlob((blob) => {
            if (blob) resolve(blob);
          }, 'image/png');
        })
      ]);

      if (!originalBlob || !maskBlob) throw new Error("Error al crear las imágenes");

      // Crear FormData para enviar a la API
      const formData = new FormData();
      formData.append('image', originalBlob, 'original.png');
      formData.append('mask', maskBlob, 'mask.png');

      // Llamar a la API de borrado de objetos
      const response = await fetch('/api/v1/images/erase', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();

      if (result.success && result.image) {
        // La API devuelve la imagen en base64, convertir a data URL
        const imageDataUrl = `data:image/png;base64,${result.image}`;

        // Crear un nuevo ad con la imagen editada
        const editedAd: GeneratedAd = {
          ...ad,
          id: `${ad.id}_edited_${Date.now()}`,
          image_url: imageDataUrl,
          timestamp: Date.now(),
          metadata: {
            ...ad.metadata,
            edited: true,
            originalId: ad.id
          }
        };

        onSave(editedAd);

        toast({
          title: "¡Imagen editada!",
          description: "Los objetos han sido borrados exitosamente",
        });
      } else {
        throw new Error(result.error || 'Error desconocido');
      }
    } catch (error) {
      console.error('Error processing image:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Enviar a Polotno para edición avanzada
  const sendToPolotno = () => {
    const canvas = canvasRef.current;
    if (!canvas) {
      toast({
        title: "Error",
        description: "Canvas no disponible",
        variant: "destructive",
      });
      return;
    }

    try {
      // Obtener la imagen actual del canvas en base64 (con o sin ediciones)
      const imageDataUrl = canvas.toDataURL('image/png', 0.9);

      console.log('🎨 Abriendo Polotno con imagen:', {
        imageSize: imageDataUrl.length,
        adId: ad.id,
        hasEdits: hasMask,
        canvasSize: `${canvas.width}x${canvas.height}`
      });

      // Guardar la imagen para Polotno y abrir modal
      setPolotnoImageData(imageDataUrl);
      setShowPolotnoModal(true);

      toast({
        title: "✅ Abriendo Polotno",
        description: "Editor avanzado cargando con tu imagen",
      });

    } catch (error) {
      console.error('❌ Error opening Polotno:', error);

      toast({
        title: "Error",
        description: "No se pudo abrir Polotno",
        variant: "destructive",
      });
    }
  };

  // Descargar imagen actual
  const downloadCurrentImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = `${ad.id}_edited.png`;
    link.href = canvas.toDataURL('image/png');
    link.click();

    toast({
      title: "Descarga iniciada",
      description: "La imagen editada se está descargando",
    });
  };

  return (
    <>
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Editor de Imagen - Anuncio {ad.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Canvas para edición */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Dibuja sobre los objetos a borrar:</h4>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm">Tamaño del pincel:</span>
                  <Slider
                    value={[brushSize]}
                    onValueChange={(value) => setBrushSize(value[0])}
                    max={50}
                    min={5}
                    step={5}
                    className="w-24"
                  />
                  <span className="text-sm w-8">{brushSize}</span>
                </div>
              </div>
            </div>

            <div className="border rounded-lg overflow-hidden bg-gray-50 flex justify-center items-center min-h-[400px]">
              <canvas
                ref={canvasRef}
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={endDrawing}
                onMouseLeave={endDrawing}
                className="cursor-crosshair border border-gray-300 rounded shadow-sm"
                style={{
                  maxWidth: "500px",
                  maxHeight: "500px",
                  display: imageLoaded ? 'block' : 'none',
                  backgroundColor: 'white'
                }}
              />
              {!imageLoaded && (
                <div className="h-64 flex items-center justify-center">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Cargando imagen...</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Controles */}
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={clearMask}
              variant="outline"
              disabled={!hasMask}
            >
              <Undo className="w-4 h-4 mr-2" />
              Limpiar Máscara
            </Button>

            <Button
              onClick={handleEraseObjects}
              disabled={!hasMask || isProcessing}
              className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]"
            >
              {isProcessing ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Eraser className="w-4 h-4 mr-2" />
              )}
              {isProcessing ? 'Procesando...' : 'Borrar Objetos'}
            </Button>

            <Button
              onClick={sendToPolotno}
              variant="outline"
              className="border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef]/5"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Editar en Polotno
            </Button>

            <Button
              onClick={downloadCurrentImage}
              variant="outline"
            >
              <Download className="w-4 h-4 mr-2" />
              Descargar
            </Button>
          </div>

          {/* Instrucciones */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="font-medium text-blue-900 mb-2">Instrucciones:</h5>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Dibuja con el mouse sobre las áreas que deseas borrar</li>
              <li>• Ajusta el tamaño del pincel según necesites</li>
              <li>• Haz clic en "Borrar Objetos" para procesar la imagen</li>
              <li>• Usa "Editar en Polotno" para edición avanzada con texto y elementos</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    {/* Modal de Polotno */}
    {showPolotnoModal && (
      <Dialog open={showPolotnoModal} onOpenChange={setShowPolotnoModal}>
      <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0">
        <DialogHeader className="p-4 border-b">
          <DialogTitle className="flex items-center justify-between">
            <span>Editor Avanzado - Polotno</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPolotnoModal(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 h-[calc(95vh-80px)]">
          {showPolotnoModal && polotnoImageData && (
            <Suspense fallback={
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p className="text-gray-600">Cargando Polotno Studio...</p>
                </div>
              </div>
            }>
              <PolotnoStudio
                width={1080}
                height={1080}
                initialImageUrl={polotnoImageData}
                platform="custom"
                className="w-full h-full"
              />
            </Suspense>
          )}
        </div>
      </DialogContent>
    </Dialog>
    )}
    </>
  );
}
