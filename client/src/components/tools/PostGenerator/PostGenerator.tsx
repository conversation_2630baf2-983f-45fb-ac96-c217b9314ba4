import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowLeft, Download, Share2, Edit3, Sparkles } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import EnhancedLoadingScreen from "./components/EnhancedLoadingScreen";
import PostCard from "./components/PostCard";
import PostEditor from "./components/PostEditor";
import { BrandAnalysis } from "../ProfessionalPostGenerator/hooks/useBrandData";

export interface BrandData {
  brandUrl: string;
  brandDescription: string;
  businessName: string;
  brandColor: string;
  voice: string;
  topics: string[];
  ctas: string[];
}

export interface PostGeneratorProps {
  brandData: BrandData;
  selectedTheme: string;
  selectedContentType?: string;
  brandAnalysis?: BrandAnalysis | null;
  analysisComplete?: boolean;
  onBack: () => void;
}

export interface GeneratedPost {
  id: string;
  content: string;
  imageUrl?: string;
  platform: string;
  template: string;
  hashtags: string[];
  cta: string;
  metadata: {
    businessName: string;
    brandColor: string;
    theme: string;
    provider?: string;
    imageGenerated?: boolean;
  };
}

const PostGenerator: React.FC<PostGeneratorProps> = ({
  brandData,
  selectedTheme,
  selectedContentType = "instagram_posts",
  brandAnalysis,
  analysisComplete,
  onBack,
}) => {
  console.log("🎨 PostGenerator component rendered with props:", {
    brandData,
    selectedTheme,
    selectedContentType,
    analysisComplete
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPosts, setGeneratedPosts] = useState<GeneratedPost[]>([]);
  const [selectedPost, setSelectedPost] = useState<GeneratedPost | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [error, setError] = useState<string>("");

  // Available templates for post generation - VIRAL CONTENT THAT PEOPLE WANT TO SEE
  const templates = [
    // VIRAL CONTENT TEMPLATES
    { id: "viral", name: "🔥 Viral", description: "Contenido viral y atractivo que genera engagement" },
    { id: "storytelling", name: "📖 Storytelling", description: "Historias cautivadoras y auténticas" },
    { id: "controversial", name: "💣 Controversial", description: "Opiniones fuertes que generan debate" },
    { id: "behind_scenes", name: "🎭 Behind Scenes", description: "Contenido auténtico detrás de cámaras" },
    { id: "trending", name: "✨ Trending", description: "Formatos y tendencias actuales" },

    // CLASSIC TEMPLATES IMPROVED
    { id: "balance", name: "⚖️ Balance", description: "Contenido profesional equilibrado" },
    { id: "educational", name: "🎓 Educational", description: "Tips y consejos valiosos" },
    { id: "motivational", name: "💪 Motivacional", description: "Contenido inspiracional" },
    { id: "meme", name: "😂 Meme", description: "Contenido divertido y relatable" },
    { id: "informativo", name: "📰 Informativo", description: "Noticias y actualizaciones" }
  ];

  // Platform options
  const platforms = [
    { id: "instagram", name: "Instagram", default: true },
    { id: "linkedin", name: "LinkedIn" },
    { id: "facebook", name: "Facebook" },
    { id: "twitter", name: "X (Twitter)" }
  ];

  const [selectedTemplate, setSelectedTemplate] = useState(templates[0].id);
  const [selectedPlatform, setSelectedPlatform] = useState("instagram");

  // Auto-start generation when component mounts
  useEffect(() => {
    if (brandData && !generatedPosts.length && !isGenerating) {
      handleGeneratePosts();
    }
  }, [brandData]);

  const handleGeneratePosts = async () => {
    setIsGenerating(true);
    setError("");

    try {
      console.log("🚀 Starting post generation with data:", {
        brandData,
        selectedTheme,
        selectedTemplate,
        selectedPlatform
      });

      const response = await fetch('/api/v1/posts/generate-batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brandInfo: {
            businessName: brandData.businessName,
            brandColor: brandData.brandColor,
            voice: brandData.voice,
            topics: brandData.topics,
            ctas: brandData.ctas,
            industry: brandAnalysis?.industry || "general",
            target_audience: brandAnalysis?.target_audience || "general audience"
          },
          designConfig: {
            selectedTheme: selectedTheme,
            platform: selectedPlatform,
            contentType: selectedContentType
          },
          generationConfig: {
            count: 3, // Generate 3 posts by default
            template: selectedTemplate,
            analysisComplete: analysisComplete || false
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log("✅ Posts generated successfully:", data);

      if (data.success && data.posts) {
        console.log("🔍 Raw API response posts:", data.posts);

        const formattedPosts: GeneratedPost[] = data.posts.map((post: any, index: number) => {
          const formattedPost = {
            id: post.id || `post_${Date.now()}_${index}`,
            content: post.text || post.content || "Generated content",
            imageUrl: post.image_url || post.imageUrl || null,
            platform: post.platform || selectedPlatform,
            template: post.template || selectedTemplate,
            hashtags: post.hashtags || [],
            cta: post.cta || brandData.ctas[0] || "Learn more",
            metadata: {
              businessName: brandData.businessName,
              brandColor: brandData.brandColor,
              theme: selectedTheme,
              provider: post.metadata?.provider || "unknown",
              imageGenerated: post.metadata?.image_generated || false
            }
          };

          console.log(`📝 Formatted post ${index + 1}:`, formattedPost);
          return formattedPost;
        });

        console.log("✅ All formatted posts:", formattedPosts);

        // Add new posts to existing ones instead of replacing
        setGeneratedPosts(prevPosts => [...prevPosts, ...formattedPosts]);
      } else {
        throw new Error(data.error || "Failed to generate posts");
      }
    } catch (error) {
      console.error("❌ Error generating posts:", error);
      setError(error instanceof Error ? error.message : "Failed to generate posts");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleEditPost = (post: GeneratedPost) => {
    setSelectedPost(post);
    setIsEditing(true);
  };

  const handleSavePost = (updatedPost: GeneratedPost) => {
    setGeneratedPosts(posts =>
      posts.map(p => p.id === updatedPost.id ? updatedPost : p)
    );
    setIsEditing(false);
    setSelectedPost(null);
  };

  const handleClearPosts = () => {
    setGeneratedPosts([]);
    setError("");
  };

  const handleGenerateMoreLikeThis = async (referencePost: GeneratedPost) => {
    console.log("🔥 BUTTON CLICKED! handleGenerateMoreLikeThis called with:", referencePost);
    setIsGenerating(true);
    setError("");

    try {
      console.log("🎯 Generating more posts like this one:", referencePost);

      const response = await fetch('/api/v1/posts/generate-similar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brandInfo: {
            businessName: brandData.businessName,
            brandColor: brandData.brandColor,
            voice: brandData.voice,
            topics: brandData.topics,
            ctas: brandData.ctas,
            industry: brandAnalysis?.industry || "general",
            target_audience: brandAnalysis?.target_audience || "general audience"
          },
          referencePost: {
            content: referencePost.content,
            template: referencePost.template,
            platform: referencePost.platform,
            metadata: referencePost.metadata,
            image_url: referencePost.imageUrl  // ✅ CRITICAL: Send the reference image URL
          },
          generationConfig: {
            count: 3, // Generate 3 similar posts
            analysisComplete: analysisComplete || false
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log("✅ Similar posts generated successfully:", data);

      if (data.success && data.posts) {
        const formattedPosts: GeneratedPost[] = data.posts.map((post: any, index: number) => ({
          id: post.id || `similar_post_${Date.now()}_${index}`,
          content: post.text || post.content || "Generated content",
          imageUrl: post.image_url || post.imageUrl || null,
          platform: post.platform || referencePost.platform,
          template: post.template || referencePost.template,
          hashtags: post.hashtags || [],
          cta: post.cta || brandData.ctas[0] || "Learn more",
          metadata: {
            businessName: brandData.businessName,
            brandColor: brandData.brandColor,
            theme: selectedTheme,
            provider: post.metadata?.provider || "unknown",
            imageGenerated: post.metadata?.image_generated || false,
            similarTo: referencePost.id // Mark as similar to reference post
          }
        }));

        // Add new similar posts to existing ones
        setGeneratedPosts(prevPosts => [...prevPosts, ...formattedPosts]);
        console.log(`✅ Added ${formattedPosts.length} posts similar to reference post`);
      } else {
        throw new Error(data.error || "Failed to generate similar posts");
      }
    } catch (error) {
      console.error("❌ Error generating similar posts:", error);
      setError(error instanceof Error ? error.message : "Failed to generate similar posts");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleExportPost = (post: GeneratedPost) => {
    // TODO: Implement export functionality
    console.log("Exporting post:", post);
  };

  const handleSharePost = (post: GeneratedPost) => {
    // TODO: Implement share functionality
    console.log("Sharing post:", post);
  };

  // Show loading screen while generating
  if (isGenerating) {
    return (
      <EnhancedLoadingScreen
        brandName={brandData.businessName || "tu marca"}
        estimatedTime={180} // 3 minutes for post generation
        isGenerating={isGenerating}
      />
    );
  }

  // Show post editor if editing
  if (isEditing && selectedPost) {
    return (
      <PostEditor
        post={selectedPost}
        brandData={brandData}
        onSave={handleSavePost}
        onCancel={() => {rles contonro, 
          setIsEditing(false);
          setSelectedPost(null);
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                onClick={onBack}
                variant="outline"
                size="sm"
                className="flex items-center"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver al Workflow
              </Button>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                  Posts Generados
                </h1>
                <p className="text-gray-600 text-sm">
                  {brandData.businessName} • {generatedPosts.length} posts creados
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleGeneratePosts}
                disabled={isGenerating}
                className="bg-gradient-to-r from-[#3018ef] to-[#4c51bf] hover:from-[#2516d6] hover:to-[#3d4db7] text-white"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                {generatedPosts.length > 0 ? 'Generar Más' : 'Generar Posts'}
              </Button>

              {generatedPosts.length > 0 && (
                <Button
                  onClick={handleClearPosts}
                  variant="outline"
                  className="border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  Limpiar Todo
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800 text-sm">{error}</p>
            <Button
              onClick={handleGeneratePosts}
              variant="outline"
              size="sm"
              className="mt-2"
            >
              Intentar de nuevo
            </Button>
          </div>
        )}

        {generatedPosts.length > 0 ? (
          <div className="w-full max-w-4xl mx-auto">
            {/* Posts Container - Vertical Stack */}
            <div className="flex flex-col space-y-8">
              <AnimatePresence>
                {generatedPosts.map((post, index) => (
                  <motion.div
                    key={post.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="w-full"
                  >
                    <PostCard
                      post={post}
                      onEdit={() => handleEditPost(post)}
                      onExport={() => handleExportPost(post)}
                      onShare={() => handleSharePost(post)}
                      onGenerateMore={() => handleGenerateMoreLikeThis(post)}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {/* Generate More Button at the bottom */}
            <div className="text-center mt-8">
              <Button
                onClick={handleGeneratePosts}
                disabled={isGenerating}
                className="bg-gradient-to-r from-[#3018ef] to-[#4c51bf] hover:from-[#2516d6] hover:to-[#3d4db7] text-white px-8 py-3"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                🚀 Generar 3 posts más
              </Button>
              <p className="text-sm text-gray-500 mt-2">
                Los nuevos posts se agregarán debajo de los existentes
              </p>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No hay posts generados
            </h3>
            <p className="text-gray-600 mb-4">
              Haz clic en "Generar Posts" para crear contenido para tu marca
            </p>
            <Button
              onClick={handleGeneratePosts}
              className="bg-gradient-to-r from-[#3018ef] to-[#4c51bf] hover:from-[#2516d6] hover:to-[#3d4db7] text-white"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Generar Posts
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PostGenerator;
