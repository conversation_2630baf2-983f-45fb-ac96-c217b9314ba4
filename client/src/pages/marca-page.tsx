import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Brain,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Copy,
  Star,
  Building,
  Users,
  Calendar,
  FileText,
  Image,
  Sparkles,
  Target,
  Palette,
  MessageSquare,
  TrendingUp,
  Settings,
  Upload,
  Download,
  Share2,
  CheckCircle,
  Zap,
  Globe,
  Layers
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { useLocation } from "wouter";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MarcaService } from "@/services/marca-service";
import { <PERSON><PERSON> } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";

// Datos de ejemplo para las marcas
const mockMarcas = [
  {
    id: 1,
    name: "Emma Studio",
    logo: "", // Usaremos el avatar con iniciales
    description: "Plataforma de marketing con IA revolucionaria",
    industry: "SaaS",
    audience: "Marketers, Agencias",
    tone: "Innovador, Profesional",
    campaigns: 24,
    assets: 156,
    lastUpdated: "2024-01-15",
    status: "active",
    color: "#3018ef"
  },
  {
    id: 2,
    name: "TechFlow",
    logo: "", // Usaremos el avatar con iniciales
    description: "Soluciones tecnológicas para empresas",
    industry: "Tecnología",
    audience: "CTOs, Desarrolladores",
    tone: "Técnico, Confiable",
    campaigns: 12,
    assets: 89,
    lastUpdated: "2024-01-10",
    status: "active",
    color: "#dd3a5a"
  },
  {
    id: 3,
    name: "GreenLife",
    logo: "", // Usaremos el avatar con iniciales
    description: "Productos ecológicos y sostenibles",
    industry: "Retail",
    audience: "Millennials, Gen Z",
    tone: "Amigable, Consciente",
    campaigns: 8,
    assets: 45,
    lastUpdated: "2024-01-08",
    status: "draft",
    color: "#10b981"
  }
];

const MarcaPage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [, navigate] = useLocation();
  const { toast } = useToast();

  // Estado para datos reales de Supabase
  const [marcas, setMarcas] = useState<Marca[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    totalCampaigns: 0,
    totalAssets: 0
  });

  // Cargar marcas desde Supabase
  const loadMarcas = async () => {
    try {
      setLoading(true);
      const [marcasData, statsData] = await Promise.all([
        MarcaService.getMarcas(),
        MarcaService.getMarcasStats()
      ]);

      setMarcas(marcasData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading marcas:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar las marcas",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Cargar datos al montar el componente
  useEffect(() => {
    loadMarcas();
  }, []);

  // Filtrar marcas
  const filteredMarcas = marcas.filter(marca =>
    marca.brand_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    marca.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Manejar acciones de marcas
  const handleDeleteMarca = async (id: string) => {
    try {
      await MarcaService.deleteMarca(id);
      toast({
        title: "Éxito",
        description: "Marca eliminada correctamente"
      });
      loadMarcas(); // Recargar lista
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo eliminar la marca",
        variant: "destructive"
      });
    }
  };

  const handleDuplicateMarca = async (id: string) => {
    try {
      await MarcaService.duplicateMarca(id);
      toast({
        title: "Éxito",
        description: "Marca duplicada correctamente"
      });
      loadMarcas(); // Recargar lista
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo duplicar la marca",
        variant: "destructive"
      });
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24
      }
    }
  };

  return (
    <DashboardLayout pageTitle="Marcas">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30">
        {/* Hero Section - Inspirado en la imagen */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          {/* Header con gradiente y decoración */}
          <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
            {/* Decoración de fondo */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-white/5 to-transparent rounded-full translate-y-24 -translate-x-24"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Brain className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Marcas
                </h1>
              </div>
              <p className="text-xl text-blue-100 mb-6 max-w-3xl">
                Donde la identidad se convierte en acción inteligente
              </p>
            </div>
          </div>

          {/* Sección explicativa - Como en la imagen */}
          <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-2xl p-8 mb-8 border border-pink-100">
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-8">
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                    <Sparkles className="h-5 w-5 text-white" />
                  </div>
                  Presentamos Marcas
                </h2>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-1 flex-shrink-0" />
                    <p className="text-gray-700">
                      <strong>Emma aprende quién es tu marca una sola vez</strong> y ejecuta con ese contexto
                      en todas las herramientas. Sin repetir briefs, sin empezar desde cero.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-1 flex-shrink-0" />
                    <p className="text-gray-700">
                      <strong>Cada campaña, visual y contenido se alinea automáticamente</strong> con el tono,
                      audiencia y personalidad de tu marca sin intervención manual.
                    </p>
                  </div>

                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-1 flex-shrink-0" />
                    <p className="text-gray-700">
                      <strong>El sistema se vuelve más inteligente con cada uso</strong>, aprendiendo qué funciona
                      y aplicando esos insights en futuras ejecuciones.
                    </p>
                  </div>

                  {/* Explanatory text section */}
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <p className="text-gray-700 text-sm">
                      <strong>¿Qué significan los números?</strong> Los <strong>Assets</strong> son todas las imágenes, textos y materiales creativos que se utilizan con cada marca. Las <strong>Campañas</strong> incluyen publicaciones, diseños, blogs y contenido de análisis creado para tu marca.
                    </p>
                  </div>
                </div>
              </div>

              {/* Elementos visuales decorativos */}
              <div className="flex-shrink-0 lg:w-80">
                <div className="relative">
                  {/* Simulación de elementos de marca */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white rounded-xl p-4 shadow-lg border">
                      <div className="w-full h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg mb-2"></div>
                      <div className="h-2 bg-gray-200 rounded mb-1"></div>
                      <div className="h-2 bg-gray-100 rounded w-3/4"></div>
                    </div>
                    <div className="bg-white rounded-xl p-4 shadow-lg border">
                      <div className="w-full h-16 bg-gradient-to-r from-orange-400 to-red-500 rounded-lg mb-2"></div>
                      <div className="h-2 bg-gray-200 rounded mb-1"></div>
                      <div className="h-2 bg-gray-100 rounded w-2/3"></div>
                    </div>
                  </div>

                  {/* Paleta de colores */}
                  <div className="flex gap-2 mt-4 justify-center">
                    <div className="w-8 h-8 bg-black rounded-lg shadow-md"></div>
                    <div className="w-8 h-8 bg-orange-500 rounded-lg shadow-md"></div>
                    <div className="w-8 h-8 bg-yellow-400 rounded-lg shadow-md"></div>
                    <div className="w-8 h-8 bg-blue-500 rounded-lg shadow-md"></div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <Button
                onClick={() => navigate("/dashboard/marca/crear")}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Plus className="h-5 w-5 mr-2" />
                Crear Marca
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div 
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          {[
            { icon: Building, label: "Marcas Activos", value: stats.active.toString(), color: "blue" },
            { icon: Sparkles, label: "Campañas Totales", value: stats.totalCampaigns.toString(), color: "purple" },
            { icon: Image, label: "Assets Generados", value: stats.totalAssets.toString(), color: "green" },
            { icon: TrendingUp, label: "Total Marcas", value: stats.total.toString(), color: "orange" }
          ].map((stat, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                    <div className={`p-3 rounded-xl bg-${stat.color}-100`}>
                      <stat.icon className={`h-6 w-6 text-${stat.color}-600`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Search and Filters - Estilo moderno */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                placeholder="Buscar una Marca"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 pr-4 py-3 border-2 border-gray-200 rounded-xl bg-white shadow-sm focus:border-blue-500 focus:ring-0 text-base"
              />
            </div>

            <div className="flex items-center gap-3">
              <Button variant="outline" className="border-2 border-gray-200 rounded-xl px-4 py-2 hover:border-blue-500 transition-colors">
                <Filter className="h-4 w-4 mr-2" />
                Filtros
              </Button>
              <Button
                onClick={() => navigate("/dashboard/marca/crear")}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Plus className="h-4 w-4 mr-2" />
                Agregar
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Cargando marcas...</p>
          </div>
        )}

        {/* Marcas Grid */}
        {!loading && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredMarcas.map((marca) => (
            <motion.div key={marca.id} variants={itemVariants}>
              <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300 group h-full flex flex-col">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* Large brand title - no icon, no badge */}
                      <CardTitle className="text-2xl font-bold text-gray-900 mb-4 text-center">
                        {marca.brand_name}
                      </CardTitle>

                      {/* Brand logo image */}
                      <div className="flex justify-center mb-4">
                        <div className="w-20 h-20 rounded-lg overflow-hidden border-2 border-gray-200 bg-gray-50 flex items-center justify-center">
                          {marca.logo_url ? (
                            <img
                              src={marca.logo_url}
                              alt={marca.brand_name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div
                              className="w-full h-full flex items-center justify-center text-white text-2xl font-bold"
                              style={{ backgroundColor: marca.primary_color }}
                            >
                              {marca.brand_name.charAt(0)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => navigate(`/dashboard/marca/${marca.id}/editar`)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDuplicateMarca(marca.id)}>
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicar
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Share2 className="h-4 w-4 mr-2" />
                          Compartir
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteMarca(marca.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Eliminar
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4 flex-1">
                  {/* Brand description */}
                  <p className="text-gray-600 text-sm line-clamp-3 text-center mb-8">
                    {marca.description}
                  </p>

                  {/* Bottom section - pushed to bottom */}
                  <div className="space-y-3 pt-4 border-t border-gray-100 mt-auto">
                    {/* Stats */}
                    <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
                      <span className="flex items-center gap-1">
                        <Target className="h-4 w-4" />
                        {marca.campaigns_count} campañas
                      </span>
                      <span className="flex items-center gap-1">
                        <Image className="h-4 w-4" />
                        {marca.assets_count} assets
                      </span>
                    </div>

                    {/* Buttons */}
                    <div className="flex flex-col gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigate(`/dashboard/marca/${marca.id}/respuestas`)}
                        className="w-full"
                      >
                        Ver Respuestas
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => navigate(`/dashboard/marca/${marca.id}/respuestas`)}
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white w-full"
                      >
                        Editar
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            ))}
          </motion.div>
        )}

        {/* Empty State */}
        {!loading && filteredMarcas.length === 0 && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <Brain className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No se encontraron marcas
            </h3>
            <p className="text-gray-500 mb-6">
              Intenta ajustar tu búsqueda o crear un nuevo marca.
            </p>
            <Button
              onClick={() => navigate("/dashboard/marca/crear")}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Crear tu primer Marca
            </Button>
          </motion.div>
        )}


      </div>
    </DashboardLayout>
  );
};

export default MarcaPage;
