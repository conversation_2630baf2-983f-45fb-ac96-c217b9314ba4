"""
Image Retrieval API endpoints for serving images from Supabase Storage.
Handles authenticated image access for Visual Complexity Analyzer and MoodBoard.
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, Response
from fastapi.responses import StreamingResponse
from typing import Dict, Any
import io

from app.core.auth import get_current_user_from_token
from app.core.supabase import SupabaseService, supabase, SUPABASE_URL
from app.core.dependencies import get_supabase_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/image/{file_path:path}")
async def get_image(
    file_path: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> StreamingResponse:
    """
    Retrieve an image from Supabase Storage with authentication.
    
    This endpoint serves images stored in the design-analysis-images bucket
    with proper authentication and user access control.
    
    Args:
        file_path: The path to the image file in storage (user_id/filename)
        current_user: Current authenticated user
        supabase_service: Supabase service instance
    
    Returns:
        StreamingResponse with the image data
    """
    try:
        logger.info(f"🖼️ Image retrieval request for: {file_path}")
        logger.info(f"👤 User: {current_user['user_id']}")
        
        # Security check: ensure user can only access their own images
        if not file_path.startswith(current_user['user_id'] + '/'):
            logger.warning(f"🚫 Access denied: User {current_user['user_id']} tried to access {file_path}")
            raise HTTPException(
                status_code=403,
                detail="Access denied: You can only access your own images"
            )
        
        # Try to download the image from Supabase Storage
        try:
            logger.info(f"📥 Downloading image from Supabase Storage: {file_path}")
            
            # Use service role client for reliable access
            download_result = supabase.storage.from_("design-analysis-images").download(file_path)
            
            if not download_result:
                logger.error(f"❌ No data returned for image: {file_path}")
                raise HTTPException(
                    status_code=404,
                    detail="Image not found in storage"
                )
            
            # Get the image data
            image_data = download_result
            
            if not image_data or len(image_data) == 0:
                logger.error(f"❌ Empty image data for: {file_path}")
                raise HTTPException(
                    status_code=404,
                    detail="Image file is empty or corrupted"
                )
            
            logger.info(f"✅ Successfully retrieved image: {len(image_data)} bytes")
            
            # Determine content type from file extension
            content_type = "image/jpeg"  # default
            if file_path.lower().endswith('.png'):
                content_type = "image/png"
            elif file_path.lower().endswith('.webp'):
                content_type = "image/webp"
            elif file_path.lower().endswith('.gif'):
                content_type = "image/gif"
            
            # Create a streaming response
            return StreamingResponse(
                io.BytesIO(image_data),
                media_type=content_type,
                headers={
                    "Cache-Control": "public, max-age=3600",  # Cache for 1 hour
                    "Content-Disposition": f"inline; filename=\"{file_path.split('/')[-1]}\"",
                    "Access-Control-Allow-Origin": "*",  # Allow CORS
                    "Access-Control-Allow-Methods": "GET",
                    "Access-Control-Allow-Headers": "Authorization, Content-Type"
                }
            )
            
        except Exception as storage_error:
            logger.error(f"💥 Storage error retrieving image {file_path}: {storage_error}")
            
            # Try alternative method with signed URL
            try:
                logger.info(f"🔄 Trying signed URL method for: {file_path}")
                
                # Create a signed URL for temporary access
                signed_url_result = supabase.storage.from_("design-analysis-images").create_signed_url(
                    file_path, 
                    expires_in=3600  # 1 hour
                )
                
                if signed_url_result and 'signedURL' in signed_url_result:
                    signed_url = signed_url_result['signedURL']
                    logger.info(f"✅ Created signed URL for: {file_path}")
                    
                    # Redirect to the signed URL
                    return Response(
                        status_code=302,
                        headers={"Location": signed_url}
                    )
                else:
                    logger.error(f"❌ Failed to create signed URL for: {file_path}")
                    raise HTTPException(
                        status_code=500,
                        detail="Failed to create signed URL for image access"
                    )
                    
            except Exception as signed_url_error:
                logger.error(f"💥 Signed URL error for {file_path}: {signed_url_error}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to retrieve image: {str(signed_url_error)}"
                )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"💥 Unexpected error retrieving image {file_path}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/image-url/{file_path:path}")
async def get_image_url(
    file_path: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Get a signed URL for an image in Supabase Storage.
    
    This endpoint creates a temporary signed URL that can be used
    to access images directly from Supabase Storage.
    
    Args:
        file_path: The path to the image file in storage
        current_user: Current authenticated user
        supabase_service: Supabase service instance
    
    Returns:
        Dictionary with the signed URL and expiration info
    """
    try:
        logger.info(f"🔗 Signed URL request for: {file_path}")
        logger.info(f"👤 User: {current_user['user_id']}")
        
        # Security check: ensure user can only access their own images
        if not file_path.startswith(current_user['user_id'] + '/'):
            logger.warning(f"🚫 Access denied: User {current_user['user_id']} tried to access {file_path}")
            raise HTTPException(
                status_code=403,
                detail="Access denied: You can only access your own images"
            )
        
        try:
            # Create a signed URL for the image
            signed_url_result = supabase.storage.from_("design-analysis-images").create_signed_url(
                file_path, 
                expires_in=3600  # 1 hour
            )
            
            if signed_url_result and 'signedURL' in signed_url_result:
                signed_url = signed_url_result['signedURL']
                logger.info(f"✅ Created signed URL for: {file_path}")
                
                return {
                    "success": True,
                    "signed_url": signed_url,
                    "expires_in": 3600,
                    "file_path": file_path
                }
            else:
                logger.error(f"❌ Failed to create signed URL for: {file_path}")
                raise HTTPException(
                    status_code=500,
                    detail="Failed to create signed URL"
                )
                
        except Exception as e:
            logger.error(f"💥 Error creating signed URL for {file_path}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create signed URL: {str(e)}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"💥 Unexpected error creating signed URL for {file_path}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/test-image-access")
async def test_image_access(
    current_user: Dict[str, Any] = Depends(get_current_user_from_token)
) -> Dict[str, Any]:
    """
    Test endpoint to verify image access functionality.
    
    Returns information about the user's images and storage access.
    """
    try:
        logger.info(f"🧪 Testing image access for user: {current_user['user_id']}")
        
        # Try to list user's images
        try:
            list_result = supabase.storage.from_("design-analysis-images").list(
                path=current_user['user_id'],
                limit=5
            )
            
            image_count = len(list_result) if list_result else 0
            
            return {
                "success": True,
                "user_id": current_user['user_id'],
                "storage_accessible": True,
                "image_count": image_count,
                "sample_images": [item['name'] for item in (list_result or [])[:3]],
                "message": f"Found {image_count} images for user"
            }
            
        except Exception as list_error:
            logger.warning(f"⚠️ Could not list images for user {current_user['user_id']}: {list_error}")
            
            return {
                "success": True,
                "user_id": current_user['user_id'],
                "storage_accessible": False,
                "image_count": 0,
                "error": str(list_error),
                "message": "Storage access limited, but image retrieval may still work"
            }
        
    except Exception as e:
        logger.error(f"💥 Error testing image access: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Test failed: {str(e)}"
        )
